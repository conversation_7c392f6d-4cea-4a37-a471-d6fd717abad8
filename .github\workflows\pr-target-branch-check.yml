name: Check PR Branching Strategy
on:
  pull_request:
    types: [opened, synchronize, reopened, edited]

jobs:
  check-branching-strategy:
    runs-on: ubuntu-latest
    steps:
      - name: Enforce branching strategy
        run: |
          if [[ "${{ github.base_ref }}" == "main" ]]; then
            if [[ "${{ github.head_ref }}" != "alpha" ]]; then
              echo "Error: Pull requests to 'main' are only allowed from the 'alpha' branch."
              exit 1
            fi
          elif [[ "${{ github.base_ref }}" != "alpha" ]]; then
            echo "Error: Pull requests must be targeted to the 'alpha' or 'main' branch."
            exit 1
          fi
          echo "Branching strategy check passed."